package constants

import "time"

// project
const (
	Project  = "platform"
	LogStore = "admin-console"
)

const (
	Md5Salt = "BKX-EldLvDS8zN3OP3y9"
)

type ContextKey string

const (
	TrackKey ContextKey = "trace_id"
)

// base
const (
	HeaderXRequestID     = "X-Request-ID"
	HeaderAuthKey        = "Authorization"
	HeaderAuthRefreshKey = "Refresh-Token"
	HeaderUserID         = "user_id"
	HeaderDeviceID       = "device_id"
	HeaderAppID          = "app_id"
	HeaderGameID         = "game_id"
	HeaderOpenID         = "open_id"
	HeaderOS             = "os"
	HeaderSign           = "sign"
	HeaderTimestamp      = "timestamp"
	HeaderChannel        = "channel"
	HeaderSource         = "source"
	HeaderDouyinOpenID   = "douyin_open_id"
)

const (
	EmptyStr = ""
)

// 特殊符号
const (
	SymbolComma      = ","
	SymbolDot        = "."
	SymbolColon      = ":"
	SymbolSemicolon  = ";"
	SymbolSlash      = "/"
	SymbolCenterLine = "-"
)

// time
const (
	DayFormat             = "2006-01-02"
	DayTimeFormat         = "2006-01-02 15:04:0"
	DayTimeSpecificFormat = "2006-01-02 15:04:05.000"
	DayTimeFormatUnix     = "2006-01-02 15:04:05"
)

const (
	OneMinute = time.Minute
	OneDay    = 24 * time.Hour
	OneWeek   = 168 * time.Hour
	TwoWeek   = 336 * time.Hour
)

// type
const (
	TrueStr  = "true"
	FalseStr = "false"
)

const (
	TrueInt  = 1
	FalseInt = 0
)

// FirstLimit limit condition
const (
	FirstLimit = 2
)

// redis key
const (
	SystemPrefix = "admin-console"

	// token blacklist
	RedisTokenBlackList = SystemPrefix + ":blacklist:token:%s"

	// SystemSecret 系统类型存储-密钥相关
	SystemSecret                   = SystemPrefix + ":secret:%s"
	SystemLoginLockKey             = SystemPrefix + ":login:lock"
	SystemLoginLockExpire          = 10
	SystemLoginSubscribeLockKey    = SystemPrefix + ":subscribe_login:lock"
	SystemLoginSubscribeLockExpire = 10
	SystemLoginDouyinLockKey       = SystemPrefix + ":douyin_login:lock"
	SystemLoginDouyinLockExpire    = 10
	SystemLoginQQLockKey           = SystemPrefix + ":qq_login:lock"
	SystemLoginQQLockExpire        = 10

	SystemProductShipmentLockKey    = SystemPrefix + ":product:lock"
	SystemProductShipmentLockExpire = 10
	SystemProductShipmentList       = SystemPrefix + ":product:list"
	SystemRedemptionLockKey         = SystemPrefix + ":redemption:lock"
	SystemRedemptionLockExpire      = 10

	SystemUserSessionFromLockKey    = SystemPrefix + ":user_session_from:lock"
	SystemUserSessionFromLockExpire = 10

	// 广告位配置缓存
	RedisAdConfigKey    = SystemPrefix + ":config:ad:%s:%s:%s" // gameID:platformType:positionID
	RedisAdConfigExpire = 300                                  // 5分钟缓存

	// 性能优化缓存键
	RedisGameExistKey     = SystemPrefix + ":game:exist:%s"     // gameID
	RedisGameInfoKey      = SystemPrefix + ":game:info:%s"      // gameID
	RedisUserInfoKey      = SystemPrefix + ":user:info:%s"      // openID
	RedisUserExistsKey    = SystemPrefix + ":user:exists:%s:%s" // gameID:openID
	RedisGameExistExpire  = 1800                                // 30分钟缓存（降低TTL）
	RedisGameInfoExpire   = 3600                                // 1小时缓存
	RedisUserInfoExpire   = 600                                 // 10分钟缓存
	RedisUserExistsExpire = 300                                 // 5分钟缓存

	// 缓存操作相关常量
	CacheRetryDelay      = 10 * time.Millisecond // 缓存重试延时
	CacheLockTimeout     = 10 * time.Second      // 缓存锁超时时间（优化为10秒）
	TestSessionKeyPrefix = "test_session_key_"   // 测试接口session key前缀

	// 游戏信息分布式锁前缀
	RedisGameInfoLockKeyPrefix = SystemPrefix + ":game_info_lock:"
	// 游戏存在性分布式锁前缀
	RedisGameExistLockKeyPrefix = SystemPrefix + ":game_exist_lock:"

	// 缓存一致性管理
	RedisCacheVersion = SystemPrefix + ":cache:version" // 缓存版本控制

	// BizConfigShare 业务类型存储-配置分享
	BizConfigShare    = SystemPrefix + ":config:share:%s:%s"
	BizConfigSwitches = SystemPrefix + ":config:switches:%s:%s:%s:%s"
)

// 敏感词发布订阅
const (
	RedisSensitivePrefix = SystemPrefix + ":sensitive:"

	RedisSensitivePublishAdd = RedisSensitivePrefix + "add"
	RedisSensitivePublishDel = RedisSensitivePrefix + "del"

	RedisSensitiveAllKeys = SystemPrefix + ":sensitive:info:*"

	RedisSensitiveConfigAllKeys = SystemPrefix + ":sensitive:config"

	RedisSensitiveConfigPublishAdd = RedisSensitivePrefix + "config:add"

	RedisSwitchPrefix        = SystemPrefix + ":switch:"
	RedisSwitchPublishAdd    = RedisSwitchPrefix + "add"
	RedisSwitchPublishDel    = RedisSwitchPrefix + "del"
	RedisSwitchAllKeys       = SystemPrefix + ":switch:info:*"
	RedisSwitchParamHSetInfo = SystemPrefix + ":switch:param:%s:%s"
)

// 工单报表发送邮件分布式锁
const (
	RedisLockWorkorderStatsEmail    = SystemPrefix + ":lock:workorder:stats:email:execution"
	LockWorkorderStatsEmailDuration = 15 * time.Minute // 工单统计邮件任务锁过期时间
)

// 授权
const (
	PlatformTypeMinigame  = "minigame"
	PlatformTypeSubscribe = "subscribe"
	PlatformTypeDouyin    = "douyin_minigame"
)

// 来源标识
const (
	SourceDouyin = "douyin"
)

const (
	ThinkingdataLogDirPath = "log_thinkingdata"
	ThinkingdataDefault    = "default"
)

// 额外数据上报相关常量
const (
	// 事件名称前缀
	SDKEventPrefix    = "sdk_"
	ServerEventPrefix = "server_"

	// 重复上报游戏ID
	DuplicateReportGameIDKofDouyin = "kof-douyin"

	// 额外数据上报App ID
	ExtraDataReportKofDouyinAppID = "0c42644fbd2542b5a1719a33aa580880"
)

// track,user_set,user_setOnce,user_add,user_unset,user_append,user_del
const (
	ThinkingdataTrack       = "track"
	ThinkingdataUserSet     = "user_set"
	ThinkingdataUserSetOnce = "user_setOnce"
	ThinkingdataUserAdd     = "user_add"
	ThinkingdataUserUnset   = "user_unset"
	ThinkingdataUserAppend  = "user_append"
	ThinkingdataUserDel     = "user_del"
)

// 内容安全
const (
	WechatSecurityVersion       = 2
	WechatSecurityMaterialScene = 1 // 场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）
)

// 兑换码
const (
	RedemptionCodeNormalType = 1
	RedemptionCodeSloganType = 2
)

// 敏感词
const (
	SensitiveWordMaxLength = 2500

	SensitiveWordIgnoreCaseOpen  = 1
	SensitiveWordIgnoreCaseClose = 2
)

// ShareScenesType 分享
var (
	ShareScenesType = map[string][]int32{
		"minigame":        {1, 2},
		"douyin_minigame": {3},
	}
)

// 客服
const (
	PushMessageMaxUser = 100

	WechatMsgTypeText        = "text"
	WechatMsgTypeImage       = "image"
	WechatMsgTypeMiniprogram = "miniprogrampage"
	WechatMsgTypeLink        = "link"
	WechatMsgTypeMenu        = "msgmenu"

	WechatMsgActionEvent            = "event"
	WechatEventMinigameDeliverGoods = "minigame_deliver_goods"
	WechatEventUserEnterTempsession = "user_enter_tempsession"
	// kf_create_session
	WechatEventKfCreateSession = "kf_create_session"
	// kf_close_session
	WechatEventKfCloseSession = "kf_close_session"
	// user_authorization_revoke
	WechatEventUserAuthorizationRevoke = "user_authorization_revoke"
	// subscribe_msg_popup_event
	WechatEventSubscribeMsgPopup = "subscribe_msg_popup_event"
	// subscribe_msg_sent_event
	WechatEventSubscribeMsgSent = "subscribe_msg_sent_event"

	WechatSceneEnterInto = 1 // 用户进入客服消息
	WechatSceneSend      = 2 // 用户发消息
	WechatSceneSendCard  = 3 // 用户发送小程序卡片
	WechatSceneMiss      = 4 // 用户发消息未命中任何文本

	// 抖音客服推送相关常量
	DouyinMsgTypeText        = "text"
	DouyinMsgTypeImage       = "image"
	DouyinMsgTypeMiniprogram = "miniprogrampage"
	DouyinMsgTypeLink        = "link"
	DouyinMsgTypeMenu        = "msgmenu"

	// 抖音消息推送类型
	DouyinMsgTypeVerifyRequest = "verify_request"      // 验证请求
	DouyinMsgTypeGiftDelivery  = "gift_delivery"       // 游戏站礼包推送
	DouyinMsgTypeIM            = "douyin_microgame_im" // 客服消息推送

	DouyinMsgActionEvent            = "event"
	DouyinEventMinigameDeliverGoods = "minigame_deliver_goods"
	DouyinEventUserEnterTempsession = "user_enter_tempsession"
	DouyinEventKfCreateSession      = "kf_create_session"
	DouyinEventKfCloseSession       = "kf_close_session"
	DouyinEventUserAuthRevoke       = "user_authorization_revoke"

	DouyinSceneEnterInto = 1 // 用户进入客服消息
	DouyinSceneSend      = 2 // 用户发消息
	DouyinSceneSendCard  = 3 // 用户发送小程序卡片
	DouyinSceneMiss      = 4 // 用户发消息未命中任何文本
)

const (
	ErrCodeNotSubscribe    = 45015 // 用户没有开通订阅后消息
	ErrCodeExceedSendLimit = 45047 // 发送消息超过限制
)

// 自定义开关
const (
	SwitchNotFound = 2

	// 自定义开关参数类型
	SwitchParamTypePlatformType = 1
	SwitchParamTypeVersion      = 2
	SwitchParamTypeNickname     = 3
	SwitchParamTypeIP           = 4
	SwitchParamTypeIPRegionID   = 5
	SwitchParamTypeUniqueID     = 6
	SwitchParamTypeChannel      = 7
	SwitchParamTypeScene        = 8
	SwitchParamTypeCustom       = 9
)

// douyin rtc serivce
type Privilege uint16

const (
	PrivPublishStream Privilege = iota

	// not exported, do not use directly
	PrivPublishAudioStream
	PrivPublishVideoStream
	PrivPublishDataStream

	PrivSubscribeStream
)

// 城市码
const (
	CityCodeUnknown = "999999"
)

// 抖音信息流游戏错误码
const (
	DouyinFeedGameErrSuccess         = 0        // 成功
	DouyinFeedGameErrInvalidParam    = 28001007 // 参数错误
	DouyinFeedGameErrSignatureFailed = 28006009 // 校验签名失败
)

// 抖音信息流游戏时间相关常量
const (
	DouyinFeedGameTimestampToleranceSeconds = 300 // 时间戳容忍范围（秒）
)

// 协议
const (
	ProtocolQiyuTickets = "index_qiyu_tickets"
	HttpPrefix          = "https://"
)

// 封号系统
const (
	BanStatusActive    = 1
	BanDurationForever = -1
)

// 验证码
const (
	// CaptchaProviderTencent 腾讯云验证码提供商
	CaptchaProviderTencent = 1
	// CaptchaProviderNetease 网易易盾验证码提供商
	CaptchaProviderNetease = 2

	// CaptchaNeteaseTicketPrefix 网易易盾验证码票据前缀
	CaptchaNeteaseTicketPrefix = "CN31_"
	// CaptchaTencentTicketPrefix 腾讯云验证码票据前缀
	CaptchaTencentTicketPrefix = "tr03"
)

// 31天毫秒
const (
	ThirtyOneDaysInMillis = 31 * 24 * 3600 * 1000
)

// 内容监控系统相关常量
const (
	// 文本来源类型
	SourceTypePublicChat           = "public_chat"
	SourceTypeAllianceChat         = "alliance_chat"
	SourceTypePrivateChat          = "private_chat"
	SourceTypeRoleName             = "role_name"
	SourceTypeAllianceName         = "alliance_name"
	SourceTypeAllianceAnnouncement = "alliance_announcement"

	// 配置键
	ConfigKeySourceMapping = "source_mapping"
)

// 用户ID映射功能相关常量 - 临时过渡逻辑
// TODO: 此功能为临时过渡逻辑，后续需要完全移除
const (
	// UserIDMappingEnabled 用户ID映射功能开关
	// 设置为true启用映射功能，false禁用
	UserIDMappingEnabled = true

	// UserIDMappingNewGameID 新游戏ID（需要进行映射的游戏）
	UserIDMappingNewGameID = "kof"

	// UserIDMappingOldGameID 旧游戏ID（映射目标游戏）
	UserIDMappingOldGameID = "hlxq"
)
